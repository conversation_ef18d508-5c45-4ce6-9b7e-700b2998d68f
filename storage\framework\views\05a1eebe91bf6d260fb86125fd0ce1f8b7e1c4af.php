<a href="<?php echo e(route('wishlists.index')); ?>" class="d-flex align-items-center text-dark" data-toggle="tooltip" data-title="<?php echo e(translate('Wishlist')); ?>" data-placement="top">
    <span class="position-relative d-inline-block">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="14.4" viewBox="0 0 16 14.4">
            <g id="_51a3dbe0e593ba390ac13cba118295e4" data-name="51a3dbe0e593ba390ac13cba118295e4" transform="translate(-3.05 -4.178)">
              <path id="Path_32649" data-name="Path 32649" d="M11.3,5.507l-.247.246L10.8,5.506A4.538,4.538,0,1,0,4.38,11.919l.247.247,6.422,6.412,6.422-6.412.247-.247A4.538,4.538,0,1,0,11.3,5.507Z" transform="translate(0 0)" fill="#919199"/>
              <path id="Path_32650" data-name="Path 32650" d="M11.3,5.507l-.247.246L10.8,5.506A4.538,4.538,0,1,0,4.38,11.919l.247.247,6.422,6.412,6.422-6.412.247-.247A4.538,4.538,0,1,0,11.3,5.507Z" transform="translate(0 0)" fill="#919199"/>
            </g>
        </svg>
        <?php if(Auth::check()): ?>
            <?php $wishlistProductCount = get_wishlists()->count(); ?>
            <?php if($wishlistProductCount > 0): ?>
                <span class="badge badge-primary badge-inline badge-pill absolute-top-right--10px"><?php echo e($wishlistProductCount); ?></span>
            <?php endif; ?>
        <?php endif; ?>
    </span>
</a>
<?php /**PATH C:\xampp\htdocs\NetbazzarB2B\resources\views/frontend/partials/wishlist.blade.php ENDPATH**/ ?>